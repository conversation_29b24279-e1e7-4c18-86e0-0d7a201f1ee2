import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/products/cart/controller.dart';
import 'package:shop_app/gen/assets.gen.dart';

class CartPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final ControllerCard = Get.find<CartPageController>();
    //final controllerProduct = Get.find<ProductDetailsPageController>();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 40),
      height: MediaQuery.of(context).size.height,
      child: Column(
        children: [
          // Expanded main content
          Expanded(
            child: Column(
              children: [
                Center(
                  child: Text(tr(LocaleKeys.My_Cart),
                      style: Theme.of(context).textTheme.headlineSmall),
                ),
                Divider(
                  color: StyleRepo.meduimGrey,
                  thickness: 2,
                ),
                const SizedBox(height: 16),
                if (ControllerCard.cartItems.isEmpty)
                  Center(
                    child: Text(
                      tr(LocaleKeys.Cart_is_empty),
                    ),
                  )
                else
                  Expanded(
                    child: ListView.separated(
                      itemCount: ControllerCard.cartItems.length,
                      separatorBuilder: (_, __) => const Divider(),
                      itemBuilder: (context, index) {
                        final product = ControllerCard.cartItems[index];
                        return ListTile(
                          leading: AppImage(
                            height: 50,
                            width: 50,
                            path: DemoMedia.getAppRandomImage,
                            type: ImageType.CachedNetwork,
                            decoration: BoxDecoration(shape: BoxShape.circle),
                          ),
                          title: Text(
                            product.name,
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          subtitle: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 45,
                                height: 45,
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(17)),
                                  border: Border.all(
                                    color: StyleRepo.lightGrey,
                                    width: 2.0,
                                  ),
                                ),
                                child: IconButton(
                                  onPressed: () {
                                    if (product.quntity.value != 0) {
                                      product.quntity.value--;
                                    }
                                  },
                                  icon: Assets.icons.minus.svg(width: 20),
                                ),
                              ),
                              SizedBox(
                                width: 45,
                                height: 45,
                                child: Center(
                                  child: Obx(
                                    () => Text(
                                      product.quntity
                                          .toString(), // هنا تعرض الكمية
                                      style: TextStyle(
                                          color: StyleRepo.black, fontSize: 20),
                                    ),
                                  ),
                                ),
                              ),
                              Container(
                                width: 45,
                                height: 45,
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(17)),
                                  border: Border.all(
                                    color: StyleRepo.lightGrey,
                                    width: 2.0,
                                  ),
                                ),
                                child: IconButton(
                                  onPressed: () {
                                    product.quntity.value++;
                                  },
                                  icon: Assets.icons.add.svg(
                                    colorFilter: ColorFilter.mode(
                                      Theme.of(context).primaryColor,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ),
                              ),
                              Obx(
                                () => Text(
                                  "   \$${product.Price.value * product.quntity.value}",
                                  style: Theme.of(context).textTheme.labelLarge,
                                ),
                              ),
                            ],
                          ),
                          trailing: IconButton(
                            icon: Icon(Icons.delete, color: Colors.green),
                            onPressed: () {
                              ControllerCard.removeFromCart(product.id);
                              Get.snackbar(
                                tr(LocaleKeys.Deleted),
                                "${product.name} " +
                                    tr(LocaleKeys.Removed_from_cart),
                              );
                              Get.forceAppUpdate();
                            },
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),

          ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(19),
                ),
                fixedSize: Size(364, 67),
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              onPressed: () {
                ControllerCard.sendcart();
                ControllerCard.clearCart();
                Get.toNamed(Pages.OrderComplete.value);
                Get.forceAppUpdate();
              },
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    children: [
                      SizedBox(width: constraints.maxWidth / 4),
                      Expanded(child: Text(tr(LocaleKeys.Go_to_Checkout))),
                      DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Obx(
                            () => Text(
                              ControllerCard.cartItems
                                  .fold(
                                    0.0,
                                    (sum, product) =>
                                        sum +
                                        (product.Price.value *
                                            product.quntity.value),
                                  )
                                  .toStringAsFixed(2),
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ))
        ],
      ),
    );
  }
}
