import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/products/cart/controller.dart';
import 'package:shop_app/core/widgets/product_card.dart';
import 'package:shop_app/features/products/PageProducts/controller.dart';
import 'package:shop_app/features/products/PageProducts/widgets/FilterShee.dart';
import 'package:shop_app/features/products/PageProducts/widgets/CustomLogoutDialog.dart';
import 'package:shop_app/gen/assets.gen.dart';

class RestaurantInPage extends StatelessWidget {
  const RestaurantInPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RestaurantInController());
    final Cartcontroller = Get.find<CartPageController>();
    return WillPopScope(
      onWillPop: () async {
        final result = await showDialog<bool>(
          context: context,
          barrierDismissible: true,
          builder: (context) {
            return CustomLogoutDialog(
              onConfirm: () {
                Get.back(result: true);
                Cartcontroller.clearCart();
              },
              onCancel: () {
                Get.back();
              },
            );
          },
        );

        return result ?? false;
      },
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          toolbarHeight: MediaQuery.of(context).size.width * .2,
          flexibleSpace: Padding(
            padding: const EdgeInsets.only(bottom: 10, top: 40.0),
            child: Center(
              child: Row(
                children: [
                  SizedBox(width: 20),
                  SizedBox(
                    width: MediaQuery.of(context).size.width * .8,
                    height: 50,
                    child: TextField(
                      controller: controller.searchTextController,
                      decoration: InputDecoration(
                        prefixIcon: Icon(Icons.search, color: StyleRepo.black),
                        suffixIcon: IconButton(
                          icon: Assets.icons.closeSearch.svg(),
                          onPressed: () {
                            controller.clearSearch();
                          },
                        ),
                        hintText: 'Search Store',
                      ),
                    ),
                  ),
                  SizedBox(width: 10),
                  IconButton(
                    onPressed: () {
                      controller.resetFilter();
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        builder: (context) => FilterSheet(),
                      );
                    },
                    icon: Assets.icons.fillter.svg(),
                  )
                ],
              ),
            ),
          ),
        ),
        body: Expanded(
          child: ObsListBuilder(
              obs: controller.products,
              builder: (context, products) {
                return GridView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 0.63),
                  children: List.generate(
                    products.length,
                    (index) => ProductCard(product: products[index]),
                  ),
                );
              }),
        ),
      ),
    );
  }
}
