import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/features/home/<USER>/TopCard.dart';
import 'package:shop_app/features/home/<USER>/content_list.dart';

import '../controller.dart';

class ProductsSection extends StatelessWidget {
  const ProductsSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomePageController>();
    return HomeContentList(
      title: tr(LocaleKeys.Products),
      seeAll: () => SizedBox(),
      content: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        scrollDirection: Axis.horizontal,
        child: ObsListBuilder(
          obs: controller.topproducts,
          builder: (context, topproducts) {
            return Row(
              children: List.generate(
                topproducts.length,
                (index) => Padding(
                  padding: index == topproducts.length - 1
                      ? EdgeInsets.zero
                      : const EdgeInsetsDirectional.only(end: 8),
                  child: TopProductCard(product: topproducts[index]),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
