import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/controller.dart';

class OrderCard extends StatefulWidget {
  final String orderNumber;
  final String orderName;
  final DateTime orderDate;
  final OrderStatus status;
  final Function(String orderNumber)? onMarkAsReceived;

  const OrderCard({
    super.key,
    required this.orderNumber,
    required this.orderName,
    required this.orderDate,
    required this.status,
    this.onMarkAsReceived,
  });

  @override
  State<OrderCard> createState() => _OrderCardState();
}

class _OrderCardState extends State<OrderCard> with TickerProviderStateMixin {
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            StyleRepo.white,
            StyleRepo.lightMetaBlue.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: StyleRepo.metaBlue.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: StyleRepo.metaBlue.withValues(alpha: 0.05),
            blurRadius: 40,
            offset: const Offset(0, 20),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: StyleRepo.metaBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Order Number
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [StyleRepo.metaBlue, StyleRepo.metaSkyBlue],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: StyleRepo.metaBlue.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Text(
                    '#$orderNumber',
                    style: const TextStyle(
                      color: StyleRepo.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                // Status Badge
                _buildStatusBadge(),
              ],
            ),

            const SizedBox(height: 16),

            // Order Name
            Text(
              orderName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: StyleRepo.black,
              ),
            ),

            const SizedBox(height: 8),

            // Order Date
            Row(
              children: [
                Icon(
                  Icons.access_time_rounded,
                  size: 16,
                  color: StyleRepo.darkGrey,
                ),
                const SizedBox(width: 6),
                Text(
                  DateFormat('dd/MM/yyyy - HH:mm').format(orderDate),
                  style: TextStyle(
                    fontSize: 14,
                    color: StyleRepo.darkGrey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Progress Bar Section
            _buildProgressBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getStatusColor(),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _getStatusColor(),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            status.label,
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حالة الطلب',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: StyleRepo.darkGrey,
          ),
        ),
        const SizedBox(height: 12),
        _buildProgressSteps(),
      ],
    );
  }

  Color _getStatusColor() {
    switch (status) {
      case OrderStatus.pending:
        return StyleRepo.metaBlue;
      case OrderStatus.processing:
        return StyleRepo.metaSkyBlue;
      case OrderStatus.completed:
        return StyleRepo.green;
      case OrderStatus.cancelled:
        return StyleRepo.red;
    }
  }

  Widget _buildProgressSteps() {
    final steps = [
      {'label': 'قيد الانتظار', 'status': OrderStatus.pending},
      {'label': 'قيد المعالجة', 'status': OrderStatus.processing},
      {'label': 'مكتمل', 'status': OrderStatus.completed},
    ];

    // Handle cancelled status separately
    if (status == OrderStatus.cancelled) {
      return _buildCancelledProgress();
    }

    return Row(
      children: [
        for (int i = 0; i < steps.length; i++) ...[
          _buildProgressStep(
            steps[i]['label'] as String,
            steps[i]['status'] as OrderStatus,
            i,
            steps.length,
          ),
          if (i < steps.length - 1) _buildProgressConnector(i),
        ],
      ],
    );
  }

  Widget _buildCancelledProgress() {
    return Row(
      children: [
        _buildProgressStep('قيد الانتظار', OrderStatus.pending, 0, 2),
        _buildProgressConnector(0, isActive: false),
        _buildProgressStep('ملغي', OrderStatus.cancelled, 1, 2),
      ],
    );
  }

  Widget _buildProgressStep(
      String label, OrderStatus stepStatus, int index, int totalSteps) {
    final isActive = _isStepActive(stepStatus);
    final isCompleted = _isStepCompleted(stepStatus);

    Color stepColor;
    if (isCompleted) {
      stepColor = StyleRepo.green;
    } else if (isActive) {
      stepColor = _getStatusColor();
    } else {
      stepColor = StyleRepo.lightGrey;
    }

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isCompleted || isActive ? stepColor : StyleRepo.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: stepColor,
                width: 2,
              ),
              boxShadow: isActive || isCompleted
                  ? [
                      BoxShadow(
                        color: stepColor.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Icon(
              _getStepIcon(stepStatus),
              size: 16,
              color: isCompleted || isActive ? StyleRepo.white : stepColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight:
                  isActive || isCompleted ? FontWeight.w600 : FontWeight.w400,
              color: isActive || isCompleted ? stepColor : StyleRepo.darkGrey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressConnector(int index, {bool isActive = true}) {
    final isConnectorActive = _isConnectorActive(index);

    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.only(bottom: 24),
        decoration: BoxDecoration(
          gradient: isConnectorActive
              ? LinearGradient(
                  colors: [StyleRepo.metaBlue, StyleRepo.metaSkyBlue],
                )
              : null,
          color: isConnectorActive ? null : StyleRepo.lightGrey,
          borderRadius: BorderRadius.circular(1),
        ),
      ),
    );
  }

  bool _isStepActive(OrderStatus stepStatus) {
    return status == stepStatus;
  }

  bool _isStepCompleted(OrderStatus stepStatus) {
    if (status == OrderStatus.cancelled) {
      return stepStatus == OrderStatus.pending;
    }

    switch (stepStatus) {
      case OrderStatus.pending:
        return status == OrderStatus.processing ||
            status == OrderStatus.completed;
      case OrderStatus.processing:
        return status == OrderStatus.completed;
      case OrderStatus.completed:
        return false;
      case OrderStatus.cancelled:
        return status == OrderStatus.cancelled;
    }
  }

  bool _isConnectorActive(int index) {
    if (status == OrderStatus.cancelled) {
      return false;
    }

    switch (index) {
      case 0: // Between pending and processing
        return status == OrderStatus.processing ||
            status == OrderStatus.completed;
      case 1: // Between processing and completed
        return status == OrderStatus.completed;
      default:
        return false;
    }
  }

  IconData _getStepIcon(OrderStatus stepStatus) {
    switch (stepStatus) {
      case OrderStatus.pending:
        return Icons.schedule_rounded;
      case OrderStatus.processing:
        return Icons.local_shipping_rounded;
      case OrderStatus.completed:
        return Icons.check_circle_rounded;
      case OrderStatus.cancelled:
        return Icons.cancel_rounded;
    }
  }
}
