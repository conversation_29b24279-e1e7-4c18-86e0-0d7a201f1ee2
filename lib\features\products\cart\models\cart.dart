import 'package:get/get.dart';

class CartModel {
  int id;
  String name;
  RxInt quntity;
  RxDouble Price;
  String? notes;
  CartModel({
    required this.id,
    required this.name,
    required int quntity,
    double? Price,
    this.notes,
  })  : quntity = quntity.obs,
        Price = (Price ?? 0).obs;

  factory CartModel.fromJson(Map<String, dynamic> json) => CartModel(
        id: json["id"],
        name: json["name"],
        quntity: json["quntity"],
        Price: json["Price"],
        notes: json["notes"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "quntity": quntity.value,
        "Price": Price.value,
        "notes": notes,
      };
}
