import 'dart:convert';

class MainProduct {
  int id;
  String name;
  String? description;
  double price;
  int quantity;
  DateTime createdAt;
  List<dynamic>? images;

  MainProduct({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    required this.quantity,
    required this.createdAt,
    this.images,
  });

  factory MainProduct.fromRawJson(String str) =>
      MainProduct.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory MainProduct.fromJson(Map<String, dynamic> json) => MainProduct(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: json["price"]?.toDouble(),
        quantity: json["quantity"],
        createdAt: DateTime.parse(json["createdAt"]),
        images: List<dynamic>.from(json["images"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "quantity": quantity,
        "createdAt": createdAt.toIso8601String(),
        "images": List<dynamic>.from(images!.map((x) => x)),
      };
}
