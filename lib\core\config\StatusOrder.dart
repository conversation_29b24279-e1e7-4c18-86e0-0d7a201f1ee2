import 'dart:ui';

import 'package:shop_app/core/style/repo.dart';

enum OrderStatus { pending, processing, completed, cancelled }

extension OrderStatusExtension on OrderStatus {
  String get label {
    switch (this) {
      case OrderStatus.pending:
        return 'قيد الانتظار';
      case OrderStatus.processing:
        return 'مقبول';
      case OrderStatus.completed:
        return 'تم الاستلام';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  Color get color {
    switch (this) {
      case OrderStatus.pending:
        return StyleRepo.indigo;
      case OrderStatus.processing:
        return StyleRepo.orange;
      case OrderStatus.completed:
        return StyleRepo.green;
      case OrderStatus.cancelled:
        return StyleRepo.red;
    }
  }
}
