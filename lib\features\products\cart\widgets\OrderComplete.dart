import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/gen/assets.gen.dart';

class OrderCompletePage extends StatelessWidget {
  const OrderCompletePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 25),
        decoration: BoxDecoration(
          color: Colors.white,
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              StyleRepo.lightGrey,
              Colors.white,
              Colors.white,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            SizedBox(
              height: 151,
            ),
            Positioned(child: Assets.icons.orderComplete.svg()),
            Text(
              "Your Order has been accepted",
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(
              height: 37,
            ),
            Text(
              "Your items has been placcd and is on it’s way to being processed",
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.labelLarge,
            ),
            SizedBox(
              height: 119,
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(19),
                ),
                fixedSize: Size(356, 67),
              ),
              onPressed: () {},
              child: Text(
                "Track Order",
                style: Theme.of(context)
                    .textTheme
                    .titleSmall!
                    .copyWith(color: StyleRepo.white),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Get.back();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                elevation: 0,
                shadowColor: Colors.transparent, // Remove any shadow
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(19),
                ),
                fixedSize: Size(356, 67),
              ).copyWith(
                overlayColor: WidgetStateProperty.all(
                    Colors.transparent), // No tap highlight
                splashFactory: NoSplash.splashFactory, // Disable ripple effect
              ),
              child: Text(
                "Back to home",
                style: Theme.of(context)
                    .textTheme
                    .titleSmall!
                    .copyWith(color: StyleRepo.black),
              ),
            ),
          ],
        ));
  }
}
