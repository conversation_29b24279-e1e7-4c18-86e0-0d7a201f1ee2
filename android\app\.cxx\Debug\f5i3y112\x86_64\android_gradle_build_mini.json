{"buildFiles": ["C:\\flutter_windows_3.29.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Flutter_Program\\Projects\\shop_app\\android\\app\\.cxx\\Debug\\f5i3y112\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Flutter_Program\\Projects\\shop_app\\android\\app\\.cxx\\Debug\\f5i3y112\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}