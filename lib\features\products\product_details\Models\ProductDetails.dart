import 'dart:convert';

class ProductsDetails {
  int id;
  String name;
  String? description;
  double price;
  int quantity;
  Category category;
  List<Image>? images;

  ProductsDetails({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    required this.quantity,
    required this.category,
    this.images,
  });

  factory ProductsDetails.fromRawJson(String str) =>
      ProductsDetails.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsDetails.fromJson(Map<String, dynamic> json) =>
      ProductsDetails(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: (json["price"]).toDouble(),
        quantity: json["quantity"],
        category: Category.fromJson(json["category"]),
        images: List<Image>.from(json["images"].map((x) => Image.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "quantity": quantity,
        "category": category.toJson(),
        "images": List<dynamic>.from(images!.map((x) => x.toJson())),
      };
}

class Category {
  String name;

  Category({
    required this.name,
  });

  factory Category.fromRawJson(String str) =>
      Category.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
      };
}

class Image {
  String image;

  Image({
    required this.image,
  });

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
      };
}
