import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

class CustomLogoutDialog extends StatelessWidget {
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const CustomLogoutDialog({
    super.key,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 30),
            decoration: BoxDecoration(
              color: StyleRepo.green,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: const Icon(
              Icons.check_circle_outline,
              color: StyleRepo.white,
              size: 60,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            tr(LocaleKeys.Are_You_Sure),
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
          ),
          const SizedBox(height: 8),
          Text(
            tr(LocaleKeys.The_cart_will_be_emptied),
            style: TextStyle(
              color: StyleRepo.darkGrey,
            ),
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                TextButton(
                  onPressed: onCancel,
                  child: Text(tr(LocaleKeys.No),
                      style: TextStyle(color: Colors.grey)),
                ),
                ElevatedButton(
                  onPressed: onConfirm,
                  style: ElevatedButton.styleFrom(
                    fixedSize: const Size(120, 50), // العرض: 120, الارتفاع: 50
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    elevation: 4,
                  ),
                  child: Text(tr(LocaleKeys.Yes)),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
