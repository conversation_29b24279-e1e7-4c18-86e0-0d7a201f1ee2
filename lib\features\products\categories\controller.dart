import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/models/product/product.dart';
import 'package:shop_app/core/services/rest_api/api_service.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/rest_api/models/request.dart';
import 'package:shop_app/core/services/rest_api/models/response_model.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/features/home/<USER>';

class CategoriesPageController extends HomePageController {
  final TextEditingController searchController = TextEditingController();
  // متغير للتحكم في حالة البحث
  RxBool isSearching = false.obs;
  // متغير لتخزين نص البحث
  RxString searchQuery = ''.obs;
  // قائمة لتخزين نتائج البحث
  ObsList<ProductsModel> searchResults = ObsList([]);

  /// دالة للبحث عن المنتجات من خلال API

  searchProducts(String query) async {
    // تحديث حالة البحث ونص البحث
    isSearching.value = true;
    searchQuery.value = query;

    // إذا كان نص البحث فارغًا، نقوم بإفراغ نتائج البحث والخروج
    if (query.isEmpty) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }

    try {
      // إرسال طلب البحث إلى API
      ResponseModel response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.categories,
          //  params: {"search": query},
          fromJson: ProductsModel.fromJson,
        ),
      );

      // التحقق من نجاح الطلب
      if (response.success) {
        // تحديث نتائج البحث
        searchResults.value = response.data;
      } else {
        // تعيين رسالة الخطأ في حالة فشل الطلب
        searchResults.error = response.message;
      }
    } catch (e) {
      // التعامل مع الأخطاء غير المتوقعة
      searchResults.error = "حدث خطأ أثناء البحث";
    }
  }

  /// دالة للبحث المحلي في المنتجات المحملة مسبقًا

  searchProductsLocally(String query) {
    // تحديث حالة البحث
    isSearching.value = true;

    // تحديث نص البحث
    searchQuery.value = query;

    // إذا كان نص البحث فارغًا، نقوم بإفراغ نتائج البحث والخروج
    if (query.isEmpty) {
      searchResults.value = [];
      isSearching.value = false;
      return;
    }

    // البحث في المنتجات المحملة مسبقًا
    if (products.value!.isNotEmpty) {
      // إنشاء قائمة جديدة من النتائج المطابقة
      searchResults.value = products.value!
          .where((product) =>
              product.name.toLowerCase().contains(query.toLowerCase()))
          .toList();

      // تحديث الواجهة
      searchResults.refresh();
    } else {
      searchResults.value = [];
      searchResults.refresh();
    }
  }

  /// دالة لإلغاء البحث والعودة إلى العرض العادي
  clearSearch() {
    isSearching.value = false;
    searchQuery.value = '';
    searchResults.value = [];
    searchController.clear();
  }

  //!SECTION
  @override
  void onClose() {
    // التخلص من متحكم النص عند إغلاق المتحكم
    searchController.dispose();
    super.onClose();
  }
}
