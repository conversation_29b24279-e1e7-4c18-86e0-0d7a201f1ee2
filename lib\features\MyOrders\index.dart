import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/MyOrders/controller.dart';
import 'package:shop_app/features/MyOrders/widgets/MyOrdersCard.dart';

class MyOrdersPage extends StatelessWidget {
  MyOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MyOrdersPageController());
    return DefaultTabController(
      length: 2,
      child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            title: Center(child: Text('My Orders')),
            bottom: TabBar(
              tabs: [
                Tab(icon: Icon(Icons.pending_actions), text: 'Current  Orders'),
                Tab(
                    icon: Icon(Icons.assignment_turned_in),
                    text: 'Previous Orders'),
              ],
            ),
          ),
          body: TabBarView(
            children: [
              // 🟣 تبويب الطلبات النشطة فقط
              Obx(() => ListView(
                    children: controller.pendingAndProcessingOrders
                        .map((order) => OrderCard(
                              orderNumber: order.orderNumber,
                              orderDate: order.orderDate,
                              status: order.status,
                            ))
                        .toList(),
                  )),

              // 🔵 تبويب الطلبات المكتملة/الملغية
              Obx(() => ListView(
                    children: controller.otherOrders
                        .map((order) => OrderCard(
                              orderNumber: order.orderNumber,
                              orderDate: order.orderDate,
                              status: order.status,
                            ))
                        .toList(),
                  )),
            ],
          )),
    );
  }
}
