import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class EditAccountPageController extends GetxController {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  late TextEditingController name, email, phoneNumber, gender, region, country;
  Rx<String> image = "".obs;

  pickImage() async {
    XFile? picked = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (picked == null) return;

    image.value = picked.path;
  }

  @override
  onInit() {
    name = TextEditingController();
    email = TextEditingController();
    phoneNumber = TextEditingController();
    gender = TextEditingController();
    region = TextEditingController();
    country = TextEditingController();
    super.onInit();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
  }

  @override
  onClose() {
    name.dispose();
    email = TextEditingController();
    phoneNumber.dispose();
    gender.dispose();
    region.dispose();
    country.dispose();
    super.onClose();
  }
}
