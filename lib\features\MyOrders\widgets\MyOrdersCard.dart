import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/MyOrders/controller.dart';

// class OrderCard extends StatelessWidget {
//   final String orderNumber;
//   final DateTime orderDate;
//   final OrderStatus status;

//   const OrderCard({
//     super.key,
//     required this.orderNumber,
//     required this.orderDate,
//     required this.status,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: BoxDecoration(
//         color: StyleRepo.white,
//         boxShadow: [
//           BoxShadow(
//             color: status.color.withValues(alpha: .2), // ✅ باستخدام extension
//             blurRadius: 13,
//             offset: const Offset(0, 3),
//           ),
//         ],
//         border: Border.all(color: StyleRepo.black),
//         borderRadius: BorderRadius.circular(16),
//       ),
//       margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
//       padding: const EdgeInsets.all(16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.end,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               // ✅ حالة الطلب مع لون مخصص
//               Container(
//                 padding:
//                     const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
//                 decoration: BoxDecoration(
//                   color: status.color.withValues(alpha: 0.1),
//                   border: Border.all(color: status.color),
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//                 child: Text(
//                   status.label,
//                   style: TextStyle(
//                     color: status.color,
//                     fontWeight: FontWeight.w600,
//                   ),
//                 ),
//               ),
//               Text(
//                 'رقم الطلب: #$orderNumber',
//                 style: Theme.of(context)
//                     .textTheme
//                     .bodyLarge!
//                     .copyWith(color: StyleRepo.black),
//               ),
//             ],
//           ),
//           const SizedBox(height: 8),

//           // ✅ تاريخ الطلب
//           Text(
//             'تاريخ الطلب: ${DateFormat('yyyy/MM/dd').format(orderDate)}',
//             style: Theme.of(context)
//                 .textTheme
//                 .bodyLarge!
//                 .copyWith(color: StyleRepo.black),
//           ),

//           const SizedBox(height: 12),
//         ],
//       ),
//     );
//   }
// }
class OrderCard extends StatelessWidget {
  final String orderNumber;
  final DateTime orderDate;
  final OrderStatus status;

  const OrderCard({
    super.key,
    required this.orderNumber,
    required this.orderDate,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MyOrdersPageController>();
    return Container(
      decoration: BoxDecoration(
        color: StyleRepo.white,
        boxShadow: [
          BoxShadow(
            color: status.color.withValues(alpha: .2), // ✅ باستخدام extension
            blurRadius: 13,
            offset: const Offset(0, 3),
          ),
        ],
        border: Border.all(color: StyleRepo.black),
        borderRadius: BorderRadius.circular(16),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent, // ⬅️ إخفاء الخط
        ),
        child: ExpansionTile(
          trailing: SizedBox.shrink(), // إلغاء السهم الافتراضي
          title: SizedBox(
            width: double.infinity,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الحالة
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: status.color.withValues(alpha: 0.1),
                        border: Border.all(color: status.color),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        status.label,
                        style: TextStyle(
                          color: status.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    status == OrderStatus.pending
                        ? Container(
                            height: 50,
                            padding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: StyleRepo.green.withValues(alpha: 0.1),
                              border: Border.all(color: StyleRepo.metaSkyBlue),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: TextButton(
                              onPressed: () {
                                controller.confirm(orderNumber);
                              },
                              child: Text(
                                "قبول",
                                style: TextStyle(
                                  color: status.color,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          )
                        : SizedBox.shrink()
                  ],
                ),
                Text(
                  'رقم الطلب: #$orderNumber',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: StyleRepo.black),
                ),
              ],
            ),
          ),
          children: [
            // التفاصيل عند التوسيع
            Padding(
              padding: EdgeInsets.all(20),
              child: Align(
                alignment: Alignment.centerRight,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تاريخ الطلب: ${DateFormat('yyyy/MM/dd').format(orderDate)}',
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(color: StyleRepo.black),
                    ),
                    SizedBox(height: 8),
                    SizedBox(height: 20),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
