/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/About.svg
  SvgGenImage get about => const SvgGenImage('assets/icons/About.svg');

  /// File path: assets/icons/DeliveryAddress.svg
  SvgGenImage get deliveryAddress =>
      const SvgGenImage('assets/icons/DeliveryAddress.svg');

  /// File path: assets/icons/Gender.svg
  SvgGenImage get gender => const SvgGenImage('assets/icons/Gender.svg');

  /// File path: assets/icons/Help.svg
  SvgGenImage get help => const SvgGenImage('assets/icons/Help.svg');

  /// File path: assets/icons/LogOut.svg
  SvgGenImage get logOut => const SvgGenImage('assets/icons/LogOut.svg');

  /// File path: assets/icons/MyDetails.svg
  SvgGenImage get myDetails => const SvgGenImage('assets/icons/MyDetails.svg');

  /// File path: assets/icons/Notification.svg
  SvgGenImage get notification =>
      const SvgGenImage('assets/icons/Notification.svg');

  /// File path: assets/icons/OrderComplete.svg
  SvgGenImage get orderComplete =>
      const SvgGenImage('assets/icons/OrderComplete.svg');

  /// File path: assets/icons/Orders.svg
  SvgGenImage get orders => const SvgGenImage('assets/icons/Orders.svg');

  /// File path: assets/icons/PaymentMethods.svg
  SvgGenImage get paymentMethods =>
      const SvgGenImage('assets/icons/PaymentMethods.svg');

  /// File path: assets/icons/PromoCard.svg
  SvgGenImage get promoCard => const SvgGenImage('assets/icons/PromoCard.svg');

  /// File path: assets/icons/add.svg
  SvgGenImage get add => const SvgGenImage('assets/icons/add.svg');

  /// File path: assets/icons/app_icon.png
  AssetGenImage get appIcon => const AssetGenImage('assets/icons/app_icon.png');

  /// File path: assets/icons/auth_backgraound.svg
  SvgGenImage get authBackgraound =>
      const SvgGenImage('assets/icons/auth_backgraound.svg');

  /// File path: assets/icons/back.svg
  SvgGenImage get back => const SvgGenImage('assets/icons/back.svg');

  /// File path: assets/icons/call.svg
  SvgGenImage get call => const SvgGenImage('assets/icons/call.svg');

  /// File path: assets/icons/camera.svg
  SvgGenImage get camera => const SvgGenImage('assets/icons/camera.svg');

  /// File path: assets/icons/cart.svg
  SvgGenImage get cart => const SvgGenImage('assets/icons/cart.svg');

  /// File path: assets/icons/circle.svg
  SvgGenImage get circle => const SvgGenImage('assets/icons/circle.svg');

  /// File path: assets/icons/closeSearch.svg
  SvgGenImage get closeSearch =>
      const SvgGenImage('assets/icons/closeSearch.svg');

  /// File path: assets/icons/explore.svg
  SvgGenImage get explore => const SvgGenImage('assets/icons/explore.svg');

  /// File path: assets/icons/file.svg
  SvgGenImage get file => const SvgGenImage('assets/icons/file.svg');

  /// File path: assets/icons/fillter.svg
  SvgGenImage get fillter => const SvgGenImage('assets/icons/fillter.svg');

  /// File path: assets/icons/guest.svg
  SvgGenImage get guest => const SvgGenImage('assets/icons/guest.svg');

  /// File path: assets/icons/heart.svg
  SvgGenImage get heart => const SvgGenImage('assets/icons/heart.svg');

  /// File path: assets/icons/home.svg
  SvgGenImage get home => const SvgGenImage('assets/icons/home.svg');

  /// File path: assets/icons/key.svg
  SvgGenImage get key => const SvgGenImage('assets/icons/key.svg');

  /// File path: assets/icons/line.svg
  SvgGenImage get line => const SvgGenImage('assets/icons/line.svg');

  /// File path: assets/icons/location.svg
  SvgGenImage get location => const SvgGenImage('assets/icons/location.svg');

  /// File path: assets/icons/logo.svg
  SvgGenImage get logo => const SvgGenImage('assets/icons/logo.svg');

  /// File path: assets/icons/logopng.png
  AssetGenImage get logopng => const AssetGenImage('assets/icons/logopng.png');

  /// File path: assets/icons/love.svg
  SvgGenImage get love => const SvgGenImage('assets/icons/love.svg');

  /// File path: assets/icons/message.svg
  SvgGenImage get message => const SvgGenImage('assets/icons/message.svg');

  /// File path: assets/icons/min.svg
  SvgGenImage get min => const SvgGenImage('assets/icons/min.svg');

  /// File path: assets/icons/minus.svg
  SvgGenImage get minus => const SvgGenImage('assets/icons/minus.svg');

  /// File path: assets/icons/national.svg
  SvgGenImage get national => const SvgGenImage('assets/icons/national.svg');

  /// File path: assets/icons/nectar.svg
  SvgGenImage get nectar => const SvgGenImage('assets/icons/nectar.svg');

  /// File path: assets/icons/pass.svg
  SvgGenImage get pass => const SvgGenImage('assets/icons/pass.svg');

  /// File path: assets/icons/person.svg
  SvgGenImage get person => const SvgGenImage('assets/icons/person.svg');

  /// File path: assets/icons/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/icons/profile.svg');

  /// File path: assets/icons/renvo.svg
  SvgGenImage get renvo => const SvgGenImage('assets/icons/renvo.svg');

  /// File path: assets/icons/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icons/share.svg');

  /// File path: assets/icons/smile.svg
  SvgGenImage get smile => const SvgGenImage('assets/icons/smile.svg');

  /// File path: assets/icons/splash.svg
  SvgGenImage get splash => const SvgGenImage('assets/icons/splash.svg');

  /// File path: assets/icons/splash_text.svg
  SvgGenImage get splashText =>
      const SvgGenImage('assets/icons/splash_text.svg');

  /// File path: assets/icons/veify.svg
  SvgGenImage get veify => const SvgGenImage('assets/icons/veify.svg');

  /// List of all assets
  List<dynamic> get values => [
    about,
    deliveryAddress,
    gender,
    help,
    logOut,
    myDetails,
    notification,
    orderComplete,
    orders,
    paymentMethods,
    promoCard,
    add,
    appIcon,
    authBackgraound,
    back,
    call,
    camera,
    cart,
    circle,
    closeSearch,
    explore,
    file,
    fillter,
    guest,
    heart,
    home,
    key,
    line,
    location,
    logo,
    logopng,
    love,
    message,
    min,
    minus,
    national,
    nectar,
    pass,
    person,
    profile,
    renvo,
    share,
    smile,
    splash,
    splashText,
    veify,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, en];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
