import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';

Widget NoResultsFound(BuildContext context) {
  return Expanded(
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: StyleRepo.darkGrey),
          <PERSON><PERSON><PERSON><PERSON>(height: 16),
          Text(
            tr(LocaleKeys.No_Results_Found),
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Sized<PERSON>ox(height: 8),
          Text(
            tr(LocaleKeys.Try_Different_Keywords),
            style: TextStyle(color: StyleRepo.darkGrey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}
