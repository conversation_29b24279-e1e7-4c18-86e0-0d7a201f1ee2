import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';

class AccountPageController extends GetxController {
  // متغيرات reactive
  final RxBool isDragging = false.obs;
  final RxDouble dragPosition = 0.0.obs;

  // بداية السحب
  void handlePanStart() {
    isDragging.value = true;
    dragPosition.value = 0.0;
  }

  // التعامل مع تحديث السحب
  void handlePanUpdate(DragUpdateDetails details) {
    dragPosition.value += details.delta.dx;
    // تحديد الحد الأدنى والأقصى للسحب (توسيع المجال)
    dragPosition.value = dragPosition.value.clamp(-62.0, 62.0);
  }

  // التعامل مع انتهاء السحب
  void handlePanEnd(DragEndDetails details, BuildContext context) {
    isDragging.value = false;

    // تحديد اللغة بناءً على موضع السحب
    bool shouldChangeToArabic = dragPosition.value > 15;
    bool shouldChangeToEnglish = dragPosition.value < -15;

    if (shouldChangeToArabic && context.locale.languageCode == 'en') {
      changeLanguage(context, 'ar');
    } else if (shouldChangeToEnglish && context.locale.languageCode == 'ar') {
      changeLanguage(context, 'en');
    }

    // إعادة تعيين موضع السحب
    dragPosition.value = 0.0;
  }

  // حساب موضع الكرة المتحركة
  double getSliderPosition(BuildContext context) {
    if (isDragging.value) {
      // أثناء السحب، تحريك الكرة مع السحب
      double basePosition = context.locale.languageCode == 'ar' ? 56.0 : 4.0;
      return (basePosition + dragPosition.value).clamp(4.0, 56.0);
    } else {
      // عند عدم السحب، موضع ثابت حسب اللغة
      return context.locale.languageCode == 'ar' ? 56.0 : 4.0;
    }
  }

  // تغيير اللغة
  void changeLanguage(BuildContext context, String languageCode) async {
    Locale newLocale = Locale(languageCode);
    await context.setLocale(newLocale);
    Get.updateLocale(newLocale);

    Get.snackbar(
      'نجح',
      'تم تغيير اللغة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: StyleRepo.purple,
      colorText: StyleRepo.white,
      duration: Duration(seconds: 2),
    );
  }
}
