import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/home/<USER>';

class AdsWidget extends StatelessWidget {
  AdsWidget({super.key});

  final controller = Get.find<HomePageController>();
  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
        obs: controller.OffersProduct,
        builder: (context, OffersProduct) {
          return Column(
            children: [
              CarouselSlider(
                options: CarouselOptions(
                  onPageChanged: (index, _) => controller.currentAd = index,
                  autoPlay: true,
                  enlargeCenterPage: true,
                  aspectRatio: 16 / 9,
                ),
                items: List.generate(
                  OffersProduct.length,
                  (index) => AspectRatio(
                    aspectRatio: 16 / 9,
                    child: AppImage(
                      width: double.infinity,
                      path: OffersProduct[index].image.isNotEmpty == true
                          ? EndPoints.baseUrl + OffersProduct[index].image
                          : '',
                      type: ImageType.CachedNetwork,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 12,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    OffersProduct.length,
                    (index) => Obx(
                      () => AnimatedContainer(
                        duration: 300.milliseconds,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        height: controller.currentAd == index ? 12 : 6,
                        width: controller.currentAd == index ? 12 : 6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: controller.currentAd == index
                              ? StyleRepo.green
                              : StyleRepo.lightGrey,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            ],
          );
        });
  }
}
