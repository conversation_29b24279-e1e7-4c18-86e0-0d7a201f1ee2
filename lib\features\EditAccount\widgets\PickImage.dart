import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/features/EditAccount/controller.dart';

import '../../../core/style/repo.dart';
import '../../../core/widgets/image.dart';
import '../../../gen/assets.gen.dart';

class PickImageMyProfile extends StatelessWidget {
  const PickImageMyProfile({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EditAccountPageController>();
    return FormField<String>(
      initialValue: controller.image.value,
      builder: (state) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              //  alignment: Alignment.center,
              children: [
                Obx(
                  () => controller.image.isEmpty
                      ? Container(
                          width: 140,
                          height: 140,
                          decoration: BoxDecoration(
                            color: StyleRepo.lightGrey,
                            shape: BoxShape.circle,
                          ),
                          child: Assets.icons.person.svg(
                            color: StyleRepo.darkGrey,
                          ))
                      : AppImage(
                          path: controller.image.value,
                          type: ImageType.File,
                          height: 140,
                          width: 140,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                        ),
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: TextButton(
                    onPressed: () async {
                      await controller.pickImage();
                      state.didChange(controller.image.value);
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                      shape: const CircleBorder(),
                    ),
                    child: Container(
                      width: 41,
                      height: 41,
                      decoration: const BoxDecoration(
                        color: StyleRepo.white,
                        shape: BoxShape.circle,
                      ),
                      child: Assets.icons.camera.svg(),
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
