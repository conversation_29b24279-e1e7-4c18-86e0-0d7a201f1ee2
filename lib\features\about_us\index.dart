import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'controller.dart';

class AboutUsPage extends StatelessWidget {
  const AboutUsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AboutUsController>(
      init: AboutUsController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Text(tr(LocaleKeys.about_us)),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Logo Section
                Center(
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(60),
                    ),
                    child: Icon(
                      Icons.store,
                      size: 60,
                      color: Colors.green,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // App Name
                Center(
                  child: Text(
                    tr(LocaleKeys.shop_app_name),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                  ),
                ),
                const SizedBox(height: 8),

                // Version
                Center(
                  child: Text(
                    tr(LocaleKeys.version) + ' 1.0.0',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ),
                const SizedBox(height: 32),

                // Description Section
                Text(
                  tr(LocaleKeys.about_description),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                Text(
                  tr(LocaleKeys.app_description),
                  style: Theme.of(context).textTheme.bodyLarge,
                  textAlign: TextAlign.justify,
                ),
                const SizedBox(height: 24),

                // Features Section
                Text(
                  tr(LocaleKeys.features),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                ...controller.features.map((feature) => Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle,
                              color: Colors.green, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              tr(feature),
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    )),
                const SizedBox(height: 24),

                // Contact Section
                Text(
                  tr(LocaleKeys.contact_us),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                ListTile(
                  leading: Icon(Icons.email, color: Colors.green),
                  title: Text('<EMAIL>'),
                  onTap: () => controller.openEmail(),
                ),
                ListTile(
                  leading: Icon(Icons.phone, color: Colors.green),
                  title: Text('****** 567 8900'),
                  onTap: () => controller.openPhone(),
                ),
                ListTile(
                  leading: Icon(Icons.language, color: Colors.green),
                  title: Text('www.shopapp.com'),
                  onTap: () => controller.openWebsite(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
