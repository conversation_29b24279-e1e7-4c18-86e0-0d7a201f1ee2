import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/gen/assets.gen.dart';
import 'location_details_controller.dart';

class LocationDetailsPage extends StatelessWidget {
  const LocationDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LocationDetailsController());

    return Scaffold(
      backgroundColor: StyleRepo.lightMetaBlue,
      appBar: AppBar(
        backgroundColor: StyleRepo.metaBlue,
        foregroundColor: StyleRepo.white,
        title: Text(
          tr(LocaleKeys.my_location),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: StyleRepo.white),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section with Meta gradient
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    StyleRepo.indigo,
                    StyleRepo.lightMetaBlue,
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Location Icon
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: StyleRepo.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: StyleRepo.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        Icons.location_on,
                        size: 40,
                        color: StyleRepo.white,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      "تم تأكيد الموقع بنجاح",
                      style: TextStyle(
                        color: StyleRepo.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "يمكنك الآن مراجعة تفاصيل الموقع وإرساله",
                      style: TextStyle(
                        color: StyleRepo.white.withValues(alpha: 0.9),
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Location Details Card
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: StyleRepo.metaBlue.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Address Section
                      Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  StyleRepo.metaBlue,
                                  StyleRepo.lightMetaBlue
                                ],
                              ),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Icon(
                              Icons.home_outlined,
                              color: StyleRepo.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "العنوان",
                                  style: TextStyle(
                                    color: StyleRepo.darkGrey,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 4),
                                Obx(() => Text(
                                      controller.address.value,
                                      style: TextStyle(
                                        color: StyleRepo.black,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    )),
                              ],
                            ),
                          ),
                        ],
                      ),

                      SizedBox(height: 20),
                      Divider(
                          color: StyleRepo.lightGrey.withValues(alpha: 0.5)),
                      SizedBox(height: 20),

                      // Additional Details Section
                      Text(
                        "تفاصيل إضافية",
                        style: TextStyle(
                          color: StyleRepo.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16),

                      // Building Number
                      TextField(
                        controller: controller.NotesAddress,
                        maxLines: 4,
                        decoration: InputDecoration(
                          hintText: "العنوان المفصل",
                          hintStyle: TextStyle(
                            color: StyleRepo.lightGrey,
                            fontSize: 14,
                          ),
                          filled: true,
                          fillColor:
                              StyleRepo.lightMetaBlue.withValues(alpha: 0.1),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: StyleRepo.metaBlue,
                              width: 2,
                            ),
                          ),
                        ),
                        style: TextStyle(
                          color: StyleRepo.black,
                          fontSize: 16,
                        ),
                      ),

                      SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),

            SizedBox(height: 30),

            // Action Buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  // Send Location Button
                  Container(
                    width: double.infinity,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [StyleRepo.metaBlue, StyleRepo.lightMetaBlue],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: StyleRepo.metaBlue.withValues(alpha: 0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: ElevatedButton(
                      onPressed: () => controller.sendLocationDetails(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.send_rounded,
                            color: StyleRepo.white,
                            size: 24,
                          ),
                          SizedBox(width: 12),
                          Text(
                            "إرسال تفاصيل الموقع",
                            style: TextStyle(
                              color: StyleRepo.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16),
                ],
              ),
            ),

            SizedBox(height: 30),
          ],
        ),
      ),
    );
  }
}
