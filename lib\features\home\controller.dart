import 'package:get/get.dart';
import 'package:shop_app/core/models/product/product.dart';
import 'package:shop_app/features/home/<USER>/OffersModel.dart';
import 'package:shop_app/features/home/<USER>/TopProduct.dart';
import '../../core/models/category.dart';
import '../../core/services/rest_api/rest_api.dart';
import '../../core/services/state_management/obs.dart';

class HomePageController extends GetxController {
  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;
  ObsList<ProductsModel> products = ObsList([]);

  fetchproducts() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.products,
        fromJson: ProductsModel.fromJson,
        copyHeader: {"Accept-Language": "en"},
      ),
    );

    if (response.success) {
      products.value = response.data;
    } else {
      products.error = response.message;
    }
  }

  //SECTION - Categories

  ObsList<CategoryModel> categories = ObsList([]);

  fetchCategories() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.categories,
        fromJson: CategoryModel.fromJson,
        copyHeader: {"Accept-Language": "en"},
      ),
    );

    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }

  ObsList<TopProducts> topproducts = ObsList([]);
  fetchTopProducts() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.home,
          fromJson: TopProducts.fromJson,
          getdata: "topProducts"),
    );

    if (response.success) {
      topproducts.value = response.data;
    } else {
      topproducts.error = response.message;
    }
  }

  ObsList<OffersModel> OffersProduct = ObsList([]);
  fetchOffersModel() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.home,
          fromJson: OffersModel.fromJson,
          getdata: "offerProducts"),
    );

    if (response.success) {
      OffersProduct.value = response.data;
    } else {
      OffersProduct.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchTopProducts();
    fetchOffersModel();
    fetchCategories();
    super.onInit();
  }
}
