import 'dart:convert';

class ProductDetails {
  int id;
  String name;
  String? description;
  double price;
  int quantity;
  Store store;
  Category category;
  List<Image>? images;

  ProductDetails({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    required this.quantity,
    required this.store,
    required this.category,
    this.images,
  });

  factory ProductDetails.fromRawJson(String str) =>
      ProductDetails.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductDetails.fromJson(Map<String, dynamic> json) => ProductDetails(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: (json["price"]).toDouble(),
        quantity: json["quantity"],
        store: Store.fromJson(json["store"]),
        category: Category.fromJson(json["category"]),
        images: List<Image>.from(json["images"].map((x) => Image.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "quantity": quantity,
        "store": store.toJson(),
        "category": category.toJson(),
        "images": List<dynamic>.from(images!.map((x) => x.toJson())),
      };
}

class Category {
  String name;

  Category({
    required this.name,
  });

  factory Category.fromRawJson(String str) =>
      Category.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
      };
}

class Image {
  String image;

  Image({
    required this.image,
  });

  factory Image.fromRawJson(String str) => Image.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
      };
}

class Store {
  String name;
  Category area;

  Store({
    required this.name,
    required this.area,
  });

  factory Store.fromRawJson(String str) => Store.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Store.fromJson(Map<String, dynamic> json) => Store(
        name: json["name"],
        area: Category.fromJson(json["area"]),
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "area": area.toJson(),
      };
}
