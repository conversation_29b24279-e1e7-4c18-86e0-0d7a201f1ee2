import 'package:flutter/widgets.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/core/services/state_management/widgets/obs_widget.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/products/categories/widgets/categorie_card.dart';
import 'package:shop_app/features/products/widgets/product_card.dart';

class SearchResults extends StatelessWidget {
  ObsList choose;
  final bool IsSearch;

  SearchResults({
    super.key,
    required this.choose,
    required this.IsSearch,
  });

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: choose,
      builder: (context, choose) {
        return Expanded(
          child: GridView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            gridDelegate: IsSearch
                ? const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 0.65)
                : const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
            children: List.generate(
              choose.length,
              (index) {
                final pair =
                    StyleRepo.colorPairs[index % StyleRepo.colorPairs.length];
                return IsSearch
                    ? ProductCard(product: choose[index])
                    : CategorieCard(
                        color_border: pair[0],
                        color_fill: pair[1],
                        Categories: choose[index],
                      );
              },
            ),
          ),
        );
      },
    );
  }
}
