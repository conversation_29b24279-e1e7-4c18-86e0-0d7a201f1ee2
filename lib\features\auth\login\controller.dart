import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:shop_app/core/config/app_builder.dart';
import 'package:shop_app/core/config/role.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/services/rest_api/models/request.dart';
import 'package:shop_app/core/services/rest_api/models/response_model.dart';

import '../../../core/services/rest_api/api_service.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  late TextEditingController userName, password;
  var passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$',
  );
  @override
  onInit() {
    userName = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  onClose() {
    userName.dispose();
    password.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        // params: {"success": true},
        body: {
          "name": userName.text,
          "password": password.text,
        },
      ),
    );
    if (response.success) {
      appBuilder.setRole(Role.user);
      appBuilder.setToken(response.data['access_token']);
      Get.toNamed(Pages.home.value);
    } else {
      Get.snackbar(LocaleKeys.error, LocaleKeys.Wrong_email);
    }
  }
}
