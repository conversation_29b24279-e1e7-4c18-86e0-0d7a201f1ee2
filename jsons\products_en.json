[{"id": 1, "name": "Burger", "description": "Delicious burger", "total_price": 10, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 2, "name": "Pizza", "description": "Tasty pizza with various toppings", "total_price": 12, "image": "https://picsum.photos/200/300", "price_before_discount": 15}, {"id": 3, "name": "Chicken Nuggets", "description": "Crispy chicken nuggets", "total_price": 8, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 4, "name": "<PERSON><PERSON>", "description": "Golden and crispy fries", "total_price": 5, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 5, "name": "Hot Dog", "description": "Classic hot dog with mustard and ketchup", "total_price": 6, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 6, "name": "Milkshake", "description": "Creamy and refreshing milkshake", "total_price": 4, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 7, "name": "Fried Chicken", "description": "Juicy fried chicken pieces", "total_price": 9, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 8, "name": "Tacos", "description": "Authentic Mexican tacos", "total_price": 10, "image": "https://picsum.photos/200/300", "price_before_discount": 12}, {"id": 9, "name": "Onion Rings", "description": "Crispy onion rings", "total_price": 4, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 10, "name": "Cheeseburger", "description": "Cheeseburger with melted cheese", "total_price": 11, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 11, "name": "Ice Cream Sundae", "description": "Indulgent ice cream sundae", "total_price": 7, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 12, "name": "Nachos", "description": "Loaded nachos with cheese and salsa", "total_price": 8, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 13, "name": "Chicken Wrap", "description": "Savory chicken wrap with fresh ingredients", "total_price": 9, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 14, "name": "Donut", "description": "Sweet and fluffy donut", "total_price": 3, "image": "https://picsum.photos/200/300", "price_before_discount": null}, {"id": 15, "name": "Soda", "description": "Refreshing soda in various flavors", "total_price": 2, "image": "https://picsum.photos/200/300", "price_before_discount": null}]