import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsController extends GetxController {
  // قائمة المميزات
  List<String> features = [
    'feature_easy_shopping',
    'feature_secure_payment',
    'feature_fast_delivery',
    'feature_customer_support',
    'feature_product_variety',
    'feature_user_friendly',
  ];

  // فتح البريد الإلكتروني
  void openEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Support Request',
    );

    try {
      await launchUrl(emailUri);
    } catch (e) {
      Get.snackbar(
        tr(LocaleKeys.error),
        tr(LocaleKeys.cannot_open_email),
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // فتح الهاتف
  void openPhone() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '+1234567890');

    try {
      await launchUrl(phoneUri);
    } catch (e) {
      Get.snackbar(
        tr(LocaleKeys.error),
        tr(LocaleKeys.cannot_open_phone),
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // فتح الموقع الإلكتروني
  void openWebsite() async {
    final Uri websiteUri = Uri.parse('https://www.shopapp.com');

    try {
      await launchUrl(websiteUri, mode: LaunchMode.externalApplication);
    } catch (e) {
      Get.snackbar(
        tr(LocaleKeys.error),
        tr(LocaleKeys.cannot_open_website),
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
