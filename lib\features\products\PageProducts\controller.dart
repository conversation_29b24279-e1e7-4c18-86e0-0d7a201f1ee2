import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:shop_app/core/models/product/product.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';
import 'package:shop_app/core/services/state_management/obs.dart';
import 'package:shop_app/features/products/cart/controller.dart';

class RestaurantInController extends GetxController {
  var isCheckedList = <String, bool>{}.obs;
  final contoller = Get.find<CartPageController>();
  List<ProductsModel>? allProducts;
  late int id;
  ObsList<ProductsModel> products = ObsList([]);

  fetchProducts() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.categorie(id),
          fromJson: ProductsModel.fromJson,
          getdata: "products"),
    );

    if (response.success) {
      products.value = response.data;
    } else {
      products.error = response.message;
    }
  }

  @override
  void onInit() {
    id = Get.arguments;
    fetchProducts();
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    contoller.clearCart();
  }

  // في تحديث المنتجات بعد الفلترة أو الاختيار:
  void updateSelectedProducts(List<String> FillterName) {
    if (products.value == null) return;
    if (allProducts == null) {
      allProducts = List.from(products.value!);
    }
    products.value = allProducts!
        .where((product) => FillterName.contains(product.name))
        .toList();

    products.refresh();
  }

  void resetFilter() {
    if (allProducts != null) {
      products.value = List.from(allProducts!); // ترجع النسخة الأصلية
      products.refresh();
      // تقدر كمان تمسح الاختيارات
      isCheckedList.value = {
        for (var p in products.value!) p.name: false,
      };
    }
  }

  TextEditingController searchTextController = TextEditingController();

  void clearSearch() {
    searchTextController.clear();
  }

  Future<bool> confirmExit(BuildContext context) async {
    bool? shouldLeave = await PanaraConfirmDialog.show(
      context,
      title: "تأكيد الخروج",
      message: "هل تريد تفريغ العربة والخروج؟",
      confirmButtonText: "نعم",
      cancelButtonText: "لا",
      panaraDialogType: PanaraDialogType.error, // يجب تحديد النوع هنا
      onTapCancel: () {
        Get.back(result: false);
      },
      onTapConfirm: () {
        contoller.clearCart(); // تأكد من كتابة اسم المتغير بشكل صحيح
        Get.back(result: true);
      },
    );

    return shouldLeave ?? false; // ترجع false إذا المستخدم ألغى
  }
}
