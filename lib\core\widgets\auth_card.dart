import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/svg_icon.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AuthCard extends StatelessWidget {
  final Widget card;
  final bool leading;

  const AuthCard({super.key, required this.card, required this.leading});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:
          Theme.of(context).primaryColor, // 👈 Important for contrast
      appBar: AppBar(
        backgroundColor: Theme.of(context).primaryColor,
        toolbarHeight: MediaQuery.of(context).size.height * 0.15,
        centerTitle: true,
        automaticallyImplyLeading: false,
        leadingWidth: 100,
        leading: leading
            ? GestureDetector(
                onTap: Get.back,
                child: Row(
                  children: [
                    SizedBox(
                      width: 6,
                    ),
                    Assets.icons.back.svg(
                        colorFilter: ColorFilter.mode(
                            StyleRepo.white, BlendMode.srcATop)),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        tr(LocaleKeys.Back),
                        style: TextStyle(
                          fontSize: 16,
                          color: StyleRepo.white,
                          //  fontWeight: FontWeight.bold
                        ),
                      ),
                    )
                  ],
                ),
              )
            : null,
        title: SvgIcon(
          icon: Assets.icons.logo,
          color: StyleRepo.white,
          size: 120,
        ),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          color: StyleRepo.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(40),
            topRight: Radius.circular(40),
          ),
        ),
        child: Stack(
          children: [Assets.icons.authBackgraound.svg(), card],
        ),
      ),
    );
  }
}
