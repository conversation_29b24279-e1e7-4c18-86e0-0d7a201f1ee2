import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/services/rest_api/constants/end_points.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/core/widgets/svg_icon.dart';
import 'package:shop_app/features/home/<USER>/TopProduct.dart';
import 'package:shop_app/gen/assets.gen.dart';

class TopProductCard extends StatelessWidget {
  final TopProducts product;
  const TopProductCard({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () =>
          Get.toNamed(Pages.product_details.value, arguments: product.id),
      child: Container(
        width: MediaQuery.sizeOf(context).width * .4,
        decoration: BoxDecoration(
          color: StyleRepo.white,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: StyleRepo.lightGrey),
        ),
        child: Column(
          children: [
            AppImage(
              height: 150,
              width: double.infinity,
              path: product.images?.isNotEmpty == true
                  ? EndPoints.baseUrl + product.images![0].image
                  : '',
              type: ImageType.CachedNetwork,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(18),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(fontSize: 20),
                  ),
                  Text(
                    product.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      Expanded(child: Text("\$${product.price}")),
                      Container(
                        height: 45,
                        width: 45,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius: BorderRadius.circular(17),
                        ),
                        child: SvgIcon(
                          icon: Assets.icons.add,
                          color: StyleRepo.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
