import 'package:flutter/material.dart';

import '../../gen/assets.gen.dart';

class SvgIconCustom extends StatelessWidget {
  final SvgGenImage icon;
  final Color? color;
  final double size;

  const SvgIconCustom({
    super.key,
    required this.icon,
    this.color,
    this.size = 23,
  });

  @override
  Widget build(BuildContext context) {
    final Color effectiveColor = color ?? IconTheme.of(context).color!;
    return Container(
      height: 45,
      width: 45,
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: BorderRadius.circular(17),
      ),
      child: icon.svg(
        colorFilter: ColorFilter.mode(effectiveColor, BlendMode.srcATop),
      ),
    );
  }
}
