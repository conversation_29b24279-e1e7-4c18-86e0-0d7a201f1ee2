import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/widgets/image.dart';

class FavoriteProductCard extends StatelessWidget {
  const FavoriteProductCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        AppImage(
          path: DemoMedia.getAppRandomImage,
          type: ImageType.CachedNetwork,
          height: 80,
          width: 80,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tr(LocaleKeys.Product_Name),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                tr(LocaleKeys.Product_Details),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        Row(
          children: [
            Text("\$100"),
            Icon(Icons.arrow_forward_ios_rounded),
          ],
        ),
      ],
    );
  }
}
