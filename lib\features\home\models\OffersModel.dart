import 'dart:convert';

class OffersModel {
  int id;

  String image;

  OffersModel({
    required this.id,
    required this.image,
  });

  factory OffersModel.fromRawJson(String str) =>
      OffersModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OffersModel.fromJson(Map<String, dynamic> json) => OffersModel(
        id: json["id"],
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "image": image,
      };
}
