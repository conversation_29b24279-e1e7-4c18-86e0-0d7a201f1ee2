import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/gen/assets.gen.dart';

class TextFormEditProfile extends StatelessWidget {
  final TextEditingController controller;
  final FormFieldValidator<String> validator;
  final Widget icon;
  final String hintText;
  final TextInputType? input;

  const TextFormEditProfile({
    super.key,
    required this.controller,
    required this.validator,
    required this.icon,
    required this.hintText,
    this.input,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: input,
      decoration: InputDecoration(
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: StyleRepo.red, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(color: StyleRepo.red, width: 1),
        ),
        prefixIcon: SizedBox(
          width: 20,
          height: 50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 8),
              icon,
              const SizedBox(width: 8),
              Assets.icons.line.svg(
                color: StyleRepo.lightGrey,
                height: 40,
              ),
            ],
          ),
        ),
        hintText: tr(hintText),
      ),
      validator: validator,
    );
  }
}
