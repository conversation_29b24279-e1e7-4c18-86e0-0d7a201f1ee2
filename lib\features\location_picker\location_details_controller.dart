import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';

class LocationDetailsController extends GetxController {
  // Text Controllers for additional details
  late TextEditingController NotesAddress;

  // Observable variables
  RxString address = ''.obs;

  @override
  void onInit() {
    NotesAddress = TextEditingController();
    address.value = Get.arguments;

    super.onInit();
  }

  sendLocationDetails() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.address,
        method: RequestMethod.Post,
        body: {
          "address": address.value,
          "NotesAddress": NotesAddress.text,
        },
      ),
    );

    if (response.success) {
    } else {}
  }

  @override
  void onClose() {
    NotesAddress.dispose();

    super.onClose();
  }
}
