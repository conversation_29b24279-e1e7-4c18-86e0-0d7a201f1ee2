[{"id": 1, "name": "Burger", "description": "Delicious burger", "total_price": 10, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": [{"default": 2, "replace_price": true, "options": [{"id": 1, "name": "Small", "price": 7}, {"id": 2, "name": "Medium", "price": 10}, {"id": 3, "name": "Large", "price": 13}]}]}, {"id": 2, "name": "Pizza", "description": "Tasty pizza with various toppings", "total_price": 12, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": 15, "isFavorited": false, "options": [{"default": 2, "replace_price": true, "options": [{"id": 1, "name": "Small", "price_before_discount": 10, "price": 8}, {"id": 2, "name": "Medium", "price_before_discount": 15, "price": 12}, {"id": 3, "name": "Large", "price_before_discount": 20, "price": 15}]}]}, {"id": 3, "name": "Chicken Nuggets", "description": "Crispy chicken nuggets", "total_price": 8, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 4, "name": "<PERSON><PERSON>", "description": "Golden and crispy fries", "total_price": 5, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 5, "name": "Hot Dog", "description": "Classic hot dog with mustard and ketchup", "total_price": 6, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 6, "name": "Milkshake", "description": "Creamy and refreshing milkshake", "total_price": 4, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 7, "name": "Fried Chicken", "description": "Juicy fried chicken pieces", "total_price": 9, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 8, "name": "Tacos", "description": "Authentic Mexican tacos", "total_price": 10, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": 12, "isFavorited": true, "options": [{"default": 2, "replace_price": true, "options": [{"id": 1, "name": "Small", "price": 9}, {"id": 2, "name": "Medium", "price_before_discount": 12, "price": 10}, {"id": 3, "name": "Large", "price": 13}]}]}, {"id": 9, "name": "Onion Rings", "description": "Crispy onion rings", "total_price": 4, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 10, "name": "Cheeseburger", "description": "Cheeseburger with melted cheese", "total_price": 11, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 11, "name": "Ice Cream Sundae", "description": "Indulgent ice cream sundae", "total_price": 7, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 12, "name": "Nachos", "description": "Loaded nachos with cheese and salsa", "total_price": 8, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 13, "name": "Chicken Wrap", "description": "Savory chicken wrap with fresh ingredients", "total_price": 9, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 14, "name": "Donut", "description": "Sweet and fluffy donut", "total_price": 3, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}, {"id": 15, "name": "Soda", "description": "Refreshing soda in various flavors", "total_price": 2, "images": ["https://picsum.photos/200/300", "https://picsum.photos/200/300", "https://picsum.photos/200/300"], "price_before_discount": null, "isFavorited": false, "options": []}]