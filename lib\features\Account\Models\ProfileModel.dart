import 'dart:convert';

class TopProducts {
  int id;
  String name;
  String barcode;
  String? image;
  String? phoneNumber;
  int? discountRate;
  String? notes;
  String? address;
  String? city;
  String? region;
  String? latitude;
  String? longitude;
  String? status;
  DateTime? createdAt;
  DateTime? updatedAt;

  TopProducts({
    required this.id,
    required this.name,
    required this.barcode,
    this.image,
    this.phoneNumber,
    this.discountRate,
    this.notes,
    this.address,
    this.city,
    this.region,
    this.latitude,
    this.longitude,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory TopProducts.fromRawJson(String str) =>
      TopProducts.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory TopProducts.fromJson(Map<String, dynamic> json) => TopProducts(
        id: json["id"],
        name: json["name"],
        barcode: json["barcode"],
        image: json["image"],
        phoneNumber: json["phoneNumber"],
        discountRate: json["discountRate"],
        notes: json["notes"],
        address: json["address"],
        city: json["city"],
        region: json["region"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        status: json["status"],
        createdAt: DateTime.parse(json["createdAt"]),
        updatedAt: DateTime.parse(json["updatedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "barcode": barcode,
        "image": image,
        "phoneNumber": phoneNumber,
        "discountRate": discountRate,
        "notes": notes,
        "address": address,
        "city": city,
        "region": region,
        "latitude": latitude,
        "longitude": longitude,
        "status": status,
        "createdAt": createdAt!.toIso8601String(),
        "updatedAt": updatedAt!.toIso8601String(),
      };
}
