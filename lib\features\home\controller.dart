import 'package:get/get.dart';
import 'package:shop_app/core/models/product/product.dart';
import '../../core/models/category.dart';
import '../../core/services/rest_api/rest_api.dart';
import '../../core/services/state_management/obs.dart';

class HomePageController extends GetxController {
  ObsList<MainProduct> products = ObsList([]);

  fetchProducts() async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.storeproduct(1),
          fromJson: MainProduct.fromJson,
          getdata: "products"),
    );

    if (response.success) {
      products.value = response.data;
    } else {
      products.error = response.message;
    }
  }
  //!SECTION

  //SECTION - Categories
  ObsList<CategoryModel> categories = ObsList([]);

  fetchCategories() async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.categories,
        fromJson: CategoryModel.from<PERSON>son,
        copyHeader: {"Accept-Language": "en"},
      ),
    );

    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchProducts();
    fetchCategories();
    super.onInit();
  }
}
