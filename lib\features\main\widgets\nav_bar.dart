import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/widgets/svg_icon.dart';
import 'package:shop_app/gen/assets.gen.dart';

import '../controller.dart';

class NavBar extends StatelessWidget {
  const NavBar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainPageController>();
    // MainPageController controller = Get.find();
    return Obx(
      () => NavigationBar(
        onDestinationSelected: (page) => controller.currentPage.value = page,
        selectedIndex: controller.currentPage.value,
        destinations: [
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.home),
            // icon: Icon(Icons.home),
            label: tr(LocaleKeys.home),
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.explore),
            label: tr(LocaleKeys.Categories),
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.cart),
            label: tr(LocaleKeys.cart),
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.heart),
            label: tr(LocaleKeys.favourite),
          ),
          NavigationDestination(
            icon: SvgIcon(icon: Assets.icons.profile),
            label: tr(LocaleKeys.account),
          ),
        ],
      ),
    );
  }
}
