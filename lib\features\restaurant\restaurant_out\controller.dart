import 'package:dio/dio.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import 'package:shop_app/core/services/pagination/controller.dart';
import 'package:shop_app/core/services/rest_api/rest_api.dart';

class RestaurantOutPageController extends GetxController {
  TextEditingController searchTextController = TextEditingController();
  late PaginationController pagerController;
  late int id;
  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
          endPoint: EndPoints.categorie(id),
          params: {"?page=": page},
          cancelToken: cancel,
          getdata: "stores"),
    );

    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }

  void clearSearch() {
    searchTextController.clear();
  }

  @override
  void onInit() {
    id = Get.arguments;
    super.onInit();
  }
}
