import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/constants/controllers_tags.dart';
import 'package:shop_app/core/models/StoresModel.dart';
import 'package:shop_app/core/services/pagination/options/list_view.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/restaurant/restaurant_out/controller.dart';
import 'package:shop_app/features/restaurant/restaurant_out/widgets/card_res.dart';
import 'package:shop_app/gen/assets.gen.dart';

class RestaurantOutPage extends StatelessWidget {
  const RestaurantOutPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RestaurantOutPageController());
    return Scaffold(
      appBar: AppBar(
        title: Center(
          child: Text(
            "Restaurant",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        automaticallyImplyLeading: false,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(40),
          child: Row(
            children: [
              IconButton(
                icon: Assets.icons.back.svg(),
                onPressed: () {
                  Get.back();
                },
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width * .8,
                height: 50,
                child: TextField(
                  controller: controller.searchTextController,
                  decoration: InputDecoration(
                    prefixIcon: Icon(Icons.search, color: StyleRepo.black),
                    suffixIcon: IconButton(
                      icon: Assets.icons.closeSearch.svg(),
                      onPressed: () {
                        controller.clearSearch();
                      },
                    ),
                    hintText: 'Search Store',
                  ),
                ),
              ),
              SizedBox(width: 10),
            ],
          ),
        ),
      ),
      body: ListViewPagination.separated(
        //
        tag: ControllersTags.Stores_pager,
        fetchApi: controller.fetchData,
        fromJson: StoresModel.fromJson,
        //
        // initialLoading: Placeholder(),
        errorWidget: (error) => Text(error),
        onControllerInit: (pagerController) =>
            controller.pagerController = pagerController,

        separatorBuilder: (_, __) => SizedBox(height: 16),
        itemBuilder: (context, index, Stores) {
          return CardRestaurant(Stores: Stores);
        },
      ),
    );
  }
}
