import 'package:get/get.dart';

class CartModel {
  int id;
  String name;
  String? image;
  RxInt quntity;
  RxDouble Price;
  String? notes;
  CartModel({
    required this.id,
    required this.name,
    this.image,
    required int quntity,
    double? Price,
    this.notes,
  })  : quntity = quntity.obs,
        Price = (Price ?? 0).obs;

  factory CartModel.fromJson(Map<String, dynamic> json) => CartModel(
        id: json["id"],
        name: json["name"],
        image: json["image"],
        quntity: json["quntity"],
        Price: json["Price"],
        notes: json["notes"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "quntity": quntity.value,
        "Price": Price.value,
        "notes": notes,
      };
}
