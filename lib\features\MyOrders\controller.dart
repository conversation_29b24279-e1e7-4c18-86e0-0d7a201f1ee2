// my_orders_page_controller.dart
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';

class MyOrdersPageController extends GetxController {
  // ✅ قائمة الطلبات
  final RxList<OrderModel> allOrders = <OrderModel>[
    OrderModel(
        orderNumber: '1234',
        orderName: 'طلب برجر وبطاطس',
        orderDate: DateTime.now().subtract(Duration(days: 2)),
        status: OrderStatus.cancelled),
    OrderModel(
        orderNumber: '1235',
        orderName: 'طلب بيتزا مارجريتا',
        orderDate: DateTime.now().subtract(Duration(days: 1)),
        status: OrderStatus.completed),
    OrderModel(
        orderNumber: '1236',
        orderName: 'طلب سلطة وعصير',
        orderDate: DateTime.now().subtract(Duration(hours: 3)),
        status: OrderStatus.pending),
    OrderModel(
        orderNumber: '1237',
        orderName: 'طلب شاورما ومشروبات',
        orderDate: DateTime.now().subtract(Duration(hours: 1)),
        status: OrderStatus.pending),
    OrderModel(
        orderNumber: '1238',
        orderName: 'طلب معكرونة وحلويات',
        orderDate: DateTime.now().subtract(Duration(minutes: 30)),
        status: OrderStatus.processing),
  ].obs;

  // ✅ الطلبات النشطة
  List<OrderModel> get pendingAndProcessingOrders => allOrders
      .where((order) =>
          order.status == OrderStatus.pending ||
          order.status == OrderStatus.processing)
      .toList();

  // ✅ الطلبات المنتهية أو الملغية
  List<OrderModel> get otherOrders => allOrders
      .where((order) =>
          order.status == OrderStatus.completed ||
          order.status == OrderStatus.cancelled)
      .toList();

  // ✅ تغيير حالة الطلب من pending إلى processing
  void confirm(String orderNumber) {
    final index =
        allOrders.indexWhere((order) => order.orderNumber == orderNumber);
    if (index != -1 && allOrders[index].status == OrderStatus.pending) {
      final updatedOrder = OrderModel(
        orderNumber: allOrders[index].orderNumber,
        orderName: allOrders[index].orderName,
        orderDate: allOrders[index].orderDate,
        status: OrderStatus.processing,
      );

      allOrders[index] = updatedOrder;
      allOrders.refresh(); // لتحديث الواجهة
    }
  }
}
// order_model.dart
