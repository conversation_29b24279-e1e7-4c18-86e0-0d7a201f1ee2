import 'dart:convert';

class TopProducts {
  int id;
  String name;
  String? description;
  double? price;
  int? quantity;
  int? rating;
  List<Image>? images;

  TopProducts({
    required this.id,
    required this.name,
    this.description,
    this.price,
    this.quantity,
    this.rating,
    this.images,
  });

  factory TopProducts.fromRawJson(String str) =>
      TopProducts.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory TopProducts.fromJson(Map<String, dynamic> json) => TopProducts(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        price: json["price"]?.toDouble(),
        quantity: json["quantity"],
        rating: json["rating"],
        images: List<Image>.from(json["images"].map((x) => Image.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "price": price,
        "quantity": quantity,
        "rating": rating,
        "images": List<dynamic>.from(images!.map((x) => x.toJson())),
      };
}

class Image {
  String image;

  Image({
    required this.image,
  });

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        image: json["image"],
      );

  Map<String, dynamic> toJson() => {
        "image": image,
      };
}
