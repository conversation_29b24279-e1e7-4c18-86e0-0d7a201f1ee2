import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/gen/assets.gen.dart';

import 'controller.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SplashScreenController());
    return Scaffold(
      backgroundColor: Theme.of(context).primaryColor,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: MediaQuery.sizeOf(context).height * .4,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(onPressed: () {}, icon: Assets.icons.splash.svg()),
              IconButton(onPressed: () {}, icon: Assets.icons.splashText.svg()),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 70),
            child: Text(
              "online grocerite",
              style: TextStyle(color: StyleRepo.white, fontSize: 25),
            ),
          )
        ],
      ),
    );
  }
}
