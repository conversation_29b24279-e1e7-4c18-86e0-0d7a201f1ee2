// my_orders_page_controller.dart
import 'package:get/get.dart';
import 'package:shop_app/core/config/StatusOrder.dart';
import 'package:shop_app/features/MyOrders/Model/OderModel.dart';

class MyOrdersPageController extends GetxController {
  // ✅ قائمة الطلبات
  final RxList<OrderModel> allOrders = <OrderModel>[
    OrderModel(
        orderNumber: '1234',
        orderDate: DateTime.now(),
        status: OrderStatus.cancelled),
    OrderModel(
        orderNumber: '1235',
        orderDate: DateTime.now(),
        status: OrderStatus.completed),
    OrderModel(
        orderNumber: '1236',
        orderDate: DateTime.now(),
        status: OrderStatus.pending),
    OrderModel(
        orderNumber: '1237',
        orderDate: DateTime.now(),
        status: OrderStatus.pending),
  ].obs;

  // ✅ الطلبات النشطة
  List<OrderModel> get pendingAndProcessingOrders => allOrders
      .where((order) =>
          order.status == OrderStatus.pending ||
          order.status == OrderStatus.processing)
      .toList();

  // ✅ الطلبات المنتهية أو الملغية
  List<OrderModel> get otherOrders => allOrders
      .where((order) =>
          order.status == OrderStatus.completed ||
          order.status == OrderStatus.cancelled)
      .toList();

  // ✅ تغيير حالة الطلب من pending إلى processing
  void confirm(String orderNumber) {
    final index =
        allOrders.indexWhere((order) => order.orderNumber == orderNumber);
    if (index != -1 && allOrders[index].status == OrderStatus.pending) {
      final updatedOrder = OrderModel(
        orderNumber: allOrders[index].orderNumber,
        orderDate: allOrders[index].orderDate,
        status: OrderStatus.processing,
      );

      allOrders[index] = updatedOrder;
      allOrders.refresh(); // لتحديث الواجهة
    }
  }
}
// order_model.dart
