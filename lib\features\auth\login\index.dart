import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/auth_card.dart';
import 'package:shop_app/gen/assets.gen.dart';

import 'controller.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(LoginPageController());
    return AuthCard(
      leading: false,
      card: Form(
        key: controller.formKey,
        child: ListView(
          padding: EdgeInsets.all(15),
          children: [
            SizedBox(height: MediaQuery.sizeOf(context).height * .01),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(tr(LocaleKeys.Login_to),
                    style: Theme.of(context).textTheme.headlineSmall),
                // SizedBox(
                //   width: 16,
                // ),
                Expanded(child: Assets.icons.smile.svg()),
              ],
            ),
            Text(tr(LocaleKeys.Enter_the_following),
                style: Theme.of(context).textTheme.bodyMedium),
            SizedBox(
              height: 25,
            ),
            Text(tr(LocaleKeys.User_Name),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              keyboardType: TextInputType.text,
              controller: controller.userName,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.call.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: tr(LocaleKeys.Ex),
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }

                return null;
              },
            ),
            SizedBox(height: 30),
            Text(tr(LocaleKeys.Password),
                style: Theme.of(context).textTheme.bodyMedium),
            const SizedBox(height: 6),
            TextFormField(
              controller: controller.password,
              decoration: InputDecoration(
                prefixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 8),
                    Assets.icons.pass.svg(),
                    SizedBox(width: 8),
                    Assets.icons.line.svg(),
                    SizedBox(width: 8),
                  ],
                ),
                hintText: '*** *** ***',
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return tr(LocaleKeys.This_field_is_required);
                }
                // if (!controller.passwordRegex.hasMatch(value)) {
                //   return tr(LocaleKeys.Password_must_be);
                // }

                return null;
              },
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Text(tr(LocaleKeys.Forget_Passwords),
                  style: Theme.of(context).textTheme.bodyMedium),
            ),
            SizedBox(height: 100),
            Center(
              child: ElevatedButton(
                onPressed: controller.confirm,
                child: Text(
                  tr(LocaleKeys.Login),
                ),
              ),
            ),
            SizedBox(height: 16),
            // TextButton(
            //   onPressed: () => Get.toNamed(Pages.register.value),
            //   child: Text(
            //     tr(LocaleKeys.Sign_up),
            //     style: Theme.of(context)
            //         .textTheme
            //         .titleSmall!
            //         .copyWith(color: StyleRepo.green),
            //   ),
            // ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Assets.icons.guest.svg(),
                SizedBox(
                  width: 10,
                ),
                IconButton(
                  onPressed: () {},
                  icon: Text(
                    tr(LocaleKeys.Join_guest),
                    style: Theme.of(context)
                        .textTheme
                        .labelMedium!
                        .copyWith(color: StyleRepo.green),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
