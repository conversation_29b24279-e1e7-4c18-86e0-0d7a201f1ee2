import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/features/Account/index.dart';
import 'package:shop_app/features/products/cart/index.dart';
import 'package:shop_app/features/products/categories/index.dart';
import 'package:shop_app/features/products/favorites/index.dart';
import 'package:shop_app/features/home/<USER>';
import 'package:shop_app/features/main/controller.dart';

import 'widgets/nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MainPageController());
    return Scaffold(
      bottomNavigationBar: NavBar(),
      body: Obx(
        () => switch (controller.currentPage.value) {
          0 => HomePage(),
          1 => CategoriesPage(),
          2 => CartPage(),
          3 => FavoritesPage(),
          4 => AccountPage(),
          _ => ColoredBox(color: StyleRepo.green),
        },
      ),
    );
  }
}
