import 'dart:convert';

class StoresModel {
  int id;
  String name;
  String address;
  String phoneNumber;
  Area area;

  StoresModel({
    required this.id,
    required this.name,
    required this.address,
    required this.phoneNumber,
    required this.area,
  });

  factory StoresModel.fromRawJson(String str) =>
      StoresModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory StoresModel.fromJson(Map<String, dynamic> json) => StoresModel(
        id: json["id"],
        name: json["name"],
        address: json["address"],
        phoneNumber: json["phoneNumber"],
        area: Area.fromJson(json["area"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "address": address,
        "phoneNumber": phoneNumber,
        "area": area.toJson(),
      };
}

class Area {
  String name;

  Area({
    required this.name,
  });

  factory Area.fromRawJson(String str) => Area.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Area.fromJson(Map<String, dynamic> json) => Area(
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
      };
}
