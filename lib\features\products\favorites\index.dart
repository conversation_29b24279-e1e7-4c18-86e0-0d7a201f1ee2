import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/localization/strings.dart';

import 'controller.dart';
import 'widgets/favorite_product_card.dart';

class FavoritesPage extends StatelessWidget {
  const FavoritesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FavoritesPageController());
    return Column(
      children: [
        SizedBox(
          height: 60 + MediaQuery.viewPaddingOf(context).top,
          child: Center(child: Text(tr(LocaleKeys.favourite))),
        ),
        Expanded(
          child: ListView.separated(
            itemCount: 10,
            separatorBuilder: (_, __) => Divider(),
            itemBuilder: (context, index) => FavoriteProductCard(),
          ),
        )
      ],
    );
  }
}
