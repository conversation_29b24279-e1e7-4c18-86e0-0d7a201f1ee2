import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shop_app/core/demo/media.dart';
import 'package:shop_app/core/localization/strings.dart';
import 'package:shop_app/core/routes/routes.dart';
import 'package:shop_app/core/style/repo.dart';
import 'package:shop_app/core/widgets/image.dart';
import 'package:shop_app/features/Account/controller.dart';
import 'package:shop_app/features/Account/widgets/ListTile.dart';
import 'package:shop_app/features/Account/widgets/menucard.dart';
import 'package:shop_app/gen/assets.gen.dart';

class AccountPage extends StatelessWidget {
  const AccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AccountPageController());
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            StyleRepo.gray,
            StyleRepo.white,
          ],
        ),
      ),
      child: ListView(
        children: [
          const SizedBox(height: 20),
          // Enhanced Profile Header
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [StyleRepo.blue, StyleRepo.green],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: StyleRepo.green.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(35),
                        border: Border.all(color: StyleRepo.white, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: AppImage(
                        width: 70,
                        height: 70,
                        path: DemoMedia.getAppRandomImage,
                        type: ImageType.CachedNetwork,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(35),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Afsar Hossen',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '<EMAIL>',
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Text(
                              '⭐ Premium Member',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Get.toNamed(Pages.editprofile.value);
                        },
                        icon: Icon(Icons.edit, color: Colors.white, size: 20),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Stats Row
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    buildStatItem(
                        '12', tr(LocaleKeys.Orders), Icons.shopping_bag),
                    buildStatItem(
                        '5', tr(LocaleKeys.favourite), Icons.favorite),
                    buildStatItem('3', tr(LocaleKeys.Rating), Icons.star),
                  ],
                ),
              ],
            ),
          ),

          // Language Switch Section
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  StyleRepo.purple,
                  StyleRepo.violet,
                  StyleRepo.indigo,
                  StyleRepo.lightBlue,
                  StyleRepo.metaBlue,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: StyleRepo.purple.withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
                BoxShadow(
                  color: StyleRepo.violet.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Row(
              children: [
                Assets.icons.file.svg(height: 40, width: 40),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tr(LocaleKeys.language),
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: StyleRepo.white,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        context.locale.languageCode == 'ar'
                            ? tr(LocaleKeys.arabic)
                            : tr(LocaleKeys.english),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: StyleRepo.white.withValues(alpha: 0.8),
                            ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onPanStart: (details) => controller.handlePanStart(),
                  onPanUpdate: (details) => controller.handlePanUpdate(details),
                  onPanEnd: (details) =>
                      controller.handlePanEnd(details, context),
                  child: Container(
                    width: 100,
                    height: 42,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          StyleRepo.white.withValues(alpha: 0.9),
                          StyleRepo.white.withValues(alpha: 0.8),
                          StyleRepo.lightMetaBlue.withValues(alpha: 0.9),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: StyleRepo.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: StyleRepo.black.withValues(alpha: 0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                        BoxShadow(
                          color: StyleRepo.white.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, -1),
                        ),
                      ],
                    ),
                    child: Obx(() => Stack(
                          children: [
                            // خلفية النصوص
                            Positioned.fill(
                              child: Row(
                                children: [
                                  Spacer(),
                                  Expanded(
                                    child: Center(
                                      child: Text(
                                        Get.locale!.languageCode == 'ar'
                                            ? 'EN'
                                            : 'ع',
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelMedium!
                                            .copyWith(color: StyleRepo.black),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // الكرة المتحركة
                            Positioned(
                              left: controller.getSliderPosition(context),
                              top: 4,
                              child: AnimatedContainer(
                                duration: controller.isDragging.value
                                    ? Duration.zero
                                    : const Duration(milliseconds: 200),
                                curve: Curves.easeInOut,
                                width: 34,
                                height: 34,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      StyleRepo.metaBlue,
                                      StyleRepo.facebookBlue,
                                      StyleRepo.metaLightBlue,
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                  borderRadius: BorderRadius.circular(17),
                                  boxShadow: [
                                    BoxShadow(
                                      color: StyleRepo.metaBlue
                                          .withValues(alpha: 0.4),
                                      blurRadius:
                                          controller.isDragging.value ? 10 : 8,
                                      offset: const Offset(0, 3),
                                    ),
                                    BoxShadow(
                                      color: StyleRepo.white
                                          .withValues(alpha: 0.3),
                                      blurRadius: 2,
                                      offset: const Offset(0, -1),
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Text(
                                    Get.locale!.languageCode == 'en'
                                        ? 'EN'
                                        : 'ع',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelSmall!
                                        .copyWith(color: StyleRepo.white),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )),
                  ),
                ),
              ],
            ),
          ),

          // Menu Cards Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            child: Column(
              children: [
                // First Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.orders.svg(),
                        tr(LocaleKeys.Orders),
                        StyleRepo.blue,
                        () => Get.toNamed(Pages.MyOrders.value),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.myDetails.svg(),
                        tr(LocaleKeys.My_Details),
                        StyleRepo.purple,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Second Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.deliveryAddress.svg(),
                        tr(LocaleKeys.Delivery_Address),
                        StyleRepo.orange,
                        () => Get.toNamed(Pages.location_picker.value),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.paymentMethods.svg(),
                        tr(LocaleKeys.Payment_Methods),
                        StyleRepo.teal,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Third Row
                Row(
                  children: [
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.about.svg(),
                        tr(LocaleKeys.About),
                        StyleRepo.cyan,
                        () => Get.toNamed(Pages.about_us.value),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: buildMenuCard(
                        context,
                        Assets.icons.notification.svg(),
                        tr(LocaleKeys.Notifications),
                        StyleRepo.indigo,
                        () {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Fourth Row
                Row(
                  children: [
                    const SizedBox(width: 12),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),

          // Logout Button
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: StyleRepo.red,
                foregroundColor: StyleRepo.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 5,
              ),
              onPressed: controller.logout,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.logOut.svg(
                    colorFilter:
                        ColorFilter.mode(StyleRepo.white, BlendMode.srcIn),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    tr(LocaleKeys.Log_Out),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
